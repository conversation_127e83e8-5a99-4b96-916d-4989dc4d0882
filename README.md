# 伙伴系统内存分配器

这是一个完整的伙伴系统内存分配器实现，采用Java语言开发，包含图形用户界面和详细的可视化展示。

## 项目概述

伙伴系统是一种经典的内存管理算法，广泛应用于操作系统内核中。本项目实现了完整的伙伴系统功能，包括：

- **内存分裂**：将大块内存分裂为两个相等的伙伴块
- **内存合并**：将相邻的空闲伙伴块合并为更大的块
- **内存分配**：根据请求大小分配合适的内存块
- **内存回收**：释放不再使用的内存块并尝试合并

## 核心特性

### 1. 算法实现
- ✅ 完整的伙伴系统分裂算法
- ✅ 智能的内存块合并机制
- ✅ 高效的内存分配策略
- ✅ 自动内存碎片整理

### 2. 可视化界面
- ✅ 直观的内存布局展示
- ✅ 线性视图和树形视图切换
- ✅ 实时的内存状态更新
- ✅ 交互式内存块选择

### 3. 功能扩展
- ✅ 内存使用统计
- ✅ 操作历史记录
- ✅ 自动演示功能
- ✅ 性能测试工具

## 文件结构

```
src/
├── Main.java                 # 主程序入口
├── BuddySystemAllocator.java # 伙伴系统核心算法
├── MemoryBlock.java          # 内存块数据结构
├── MemoryVisualizer.java     # 内存可视化组件
├── BuddySystemGUI.java       # 图形用户界面
└── AllocationDemo.java       # 演示和测试程序
```

## 运行方法

### 方法1：直接运行Main类
```bash
cd src
javac *.java
java Main
```

### 方法2：运行图形界面
```bash
cd src
javac *.java
java BuddySystemGUI
```

### 方法3：运行演示程序
```bash
cd src
javac *.java
java AllocationDemo
```

## 使用说明

### 图形界面操作

1. **内存分配**
   - 在"请求大小"字段输入需要分配的内存大小
   - 在"请求ID"字段输入标识符
   - 点击"分配内存"按钮

2. **内存释放**
   - 从"已分配块"下拉框选择要释放的内存块
   - 点击"释放内存"按钮

3. **视图切换**
   - 勾选"显示地址"显示内存地址信息
   - 勾选"树形视图"切换到树形显示模式

4. **系统配置**
   - 使用滑块调整总内存大小
   - 点击"运行演示"查看自动演示

5. **信息查看**
   - 点击内存块查看详细信息
   - 查看"历史"选项卡了解操作记录
   - 底部状态栏显示实时统计信息

### 演示功能

程序包含多个演示场景：

1. **基本分配测试** - 展示基本的分配和释放操作
2. **内存碎片测试** - 演示内存碎片的产生和影响
3. **块合并测试** - 展示伙伴块的合并过程
4. **边界情况测试** - 测试各种边界条件
5. **性能测试** - 评估算法性能

## 算法原理

### 伙伴系统工作原理

1. **初始化**：创建一个大的连续内存块作为根节点

2. **分配过程**：
   - 找到最小的满足要求的2的幂大小
   - 查找合适的空闲块
   - 如果块太大，递归分裂直到合适大小
   - 标记块为已分配

3. **释放过程**：
   - 标记块为空闲
   - 检查伙伴块是否也空闲
   - 如果是，合并两个伙伴块
   - 递归向上尝试合并

4. **分裂规则**：
   - 只有空闲块可以分裂
   - 分裂产生两个相等大小的伙伴块
   - 伙伴块地址相邻

5. **合并规则**：
   - 只有相邻的空闲伙伴块可以合并
   - 合并后形成更大的空闲块
   - 递归检查是否可以继续向上合并

### 数据结构设计

- **MemoryBlock**：表示内存块，包含地址、大小、状态等信息
- **树形结构**：使用二叉树表示伙伴关系
- **链表管理**：维护空闲块链表用于快速查找

## 技术特点

### 1. 算法优势
- **快速分配**：O(log n)时间复杂度
- **自动合并**：减少外部碎片
- **简单实现**：算法逻辑清晰

### 2. 界面设计
- **分层展示**：区分基础功能和扩展功能
- **实时更新**：内存状态实时可视化
- **交互友好**：支持鼠标点击和键盘操作

### 3. 扩展功能
- **统计分析**：内存利用率、碎片率统计
- **历史记录**：完整的操作日志
- **性能监控**：分配/释放时间测量

## 学习价值

本项目适合用于：

1. **操作系统课程**：理解内存管理原理
2. **数据结构学习**：掌握二叉树应用
3. **算法设计**：学习分治思想
4. **GUI编程**：Java Swing界面开发
5. **软件工程**：完整项目开发流程

## 扩展建议

可以进一步扩展的功能：

1. **多种分配策略**：首次适应、最佳适应等
2. **内存压缩**：减少内部碎片
3. **并发支持**：多线程安全的内存分配
4. **持久化**：保存/加载内存状态
5. **网络功能**：分布式内存管理

## 技术要求

- **Java版本**：JDK 8或更高版本
- **图形界面**：支持Swing的Java环境
- **内存要求**：至少512MB可用内存

## 作者信息

本项目是操作系统课程的实践项目，展示了伙伴系统内存分配器的完整实现。

---

**注意**：本项目仅用于教学和学习目的，不建议在生产环境中使用。

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;
import java.util.Map;

/**
 * 内存可视化组件
 * 以图形方式展示伙伴系统的内存布局和状态
 */
public class MemoryVisualizer extends JPanel {
    private BuddySystemAllocator allocator;
    private int blockHeight = 30;
    private int levelSpacing = 40;
    private int blockSpacing = 2;
    private MemoryBlock selectedBlock;
    private boolean showAddresses = true;
    private boolean showTree = false;
    
    // 颜色定义
    private static final Color FREE_COLOR = new Color(200, 255, 200);
    private static final Color ALLOCATED_COLOR = new Color(255, 200, 200);
    private static final Color SELECTED_COLOR = new Color(255, 255, 100);
    private static final Color BORDER_COLOR = Color.BLACK;
    private static final Color TEXT_COLOR = Color.BLACK;
    
    public MemoryVisualizer(BuddySystemAllocator allocator) {
        this.allocator = allocator;
        setPreferredSize(new Dimension(800, 600));
        setBackground(Color.WHITE);
        
        // 添加鼠标监听器用于选择块
        addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                handleMouseClick(e.getX(), e.getY());
            }
        });
    }
    
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g;
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        if (showTree) {
            drawTreeView(g2d);
        } else {
            drawLinearView(g2d);
        }
        
        drawLegend(g2d);
        drawStats(g2d);
    }
    
    /**
     * 绘制线性内存视图
     */
    private void drawLinearView(Graphics2D g2d) {
        List<MemoryBlock> leafBlocks = getLeafBlocks();
        if (leafBlocks.isEmpty()) return;
        
        int totalWidth = getWidth() - 40;
        int startX = 20;
        int startY = 50;
        
        // 按地址排序
        leafBlocks.sort((a, b) -> Integer.compare(a.getStartAddress(), b.getStartAddress()));
        
        for (MemoryBlock block : leafBlocks) {
            int blockWidth = (int) ((double) block.getSize() / allocator.getTotalSize() * totalWidth);
            
            // 选择颜色
            Color blockColor;
            if (block == selectedBlock) {
                blockColor = SELECTED_COLOR;
            } else if (block.isAllocated()) {
                blockColor = ALLOCATED_COLOR;
            } else {
                blockColor = FREE_COLOR;
            }
            
            // 绘制块
            g2d.setColor(blockColor);
            g2d.fillRect(startX, startY, blockWidth, blockHeight);
            
            g2d.setColor(BORDER_COLOR);
            g2d.drawRect(startX, startY, blockWidth, blockHeight);
            
            // 绘制文本信息
            g2d.setColor(TEXT_COLOR);
            g2d.setFont(new Font("Arial", Font.PLAIN, 10));
            
            String text = "";
            if (showAddresses) {
                text = String.format("%d-%d", block.getStartAddress(), block.getEndAddress());
            }
            if (block.isAllocated() && block.getAllocatedTo() != null) {
                text += "\n" + block.getAllocatedTo();
            }
            
            // 绘制多行文本
            String[] lines = text.split("\n");
            FontMetrics fm = g2d.getFontMetrics();
            int textY = startY + (blockHeight - lines.length * fm.getHeight()) / 2 + fm.getAscent();
            
            for (String line : lines) {
                int textWidth = fm.stringWidth(line);
                int textX = startX + (blockWidth - textWidth) / 2;
                if (textWidth <= blockWidth - 4) {
                    g2d.drawString(line, textX, textY);
                }
                textY += fm.getHeight();
            }
            
            startX += blockWidth + blockSpacing;
        }
        
        // 绘制地址标尺
        drawAddressRuler(g2d, 20, startY + blockHeight + 10, totalWidth);
    }
    
    /**
     * 绘制树形视图
     */
    private void drawTreeView(Graphics2D g2d) {
        if (allocator.getRootBlock() != null) {
            drawTreeNode(g2d, allocator.getRootBlock(), getWidth() / 2, 50, getWidth() / 4);
        }
    }
    
    /**
     * 递归绘制树节点
     */
    private void drawTreeNode(Graphics2D g2d, MemoryBlock block, int x, int y, int spacing) {
        if (block == null) return;
        
        int nodeWidth = Math.max(80, block.getSize() / 4);
        int nodeHeight = 25;
        
        // 选择颜色
        Color nodeColor;
        if (block == selectedBlock) {
            nodeColor = SELECTED_COLOR;
        } else if (block.isAllocated()) {
            nodeColor = ALLOCATED_COLOR;
        } else if (block.isLeaf()) {
            nodeColor = FREE_COLOR;
        } else {
            nodeColor = Color.LIGHT_GRAY;
        }
        
        // 绘制节点
        g2d.setColor(nodeColor);
        g2d.fillRect(x - nodeWidth/2, y, nodeWidth, nodeHeight);
        
        g2d.setColor(BORDER_COLOR);
        g2d.drawRect(x - nodeWidth/2, y, nodeWidth, nodeHeight);
        
        // 绘制节点信息
        g2d.setColor(TEXT_COLOR);
        g2d.setFont(new Font("Arial", Font.PLAIN, 9));
        
        String text = String.format("Size:%d", block.getSize());
        if (block.isAllocated()) {
            text += " [" + block.getAllocatedTo() + "]";
        }
        
        FontMetrics fm = g2d.getFontMetrics();
        int textWidth = fm.stringWidth(text);
        if (textWidth <= nodeWidth - 4) {
            g2d.drawString(text, x - textWidth/2, y + nodeHeight/2 + fm.getAscent()/2);
        }
        
        // 绘制子节点连线和子节点
        if (block.getLeftChild() != null) {
            int childY = y + levelSpacing;
            int leftX = x - spacing/2;
            int rightX = x + spacing/2;
            
            // 绘制连线
            g2d.setColor(BORDER_COLOR);
            g2d.drawLine(x, y + nodeHeight, leftX, childY);
            g2d.drawLine(x, y + nodeHeight, rightX, childY);
            
            // 递归绘制子节点
            drawTreeNode(g2d, block.getLeftChild(), leftX, childY, spacing/2);
            drawTreeNode(g2d, block.getRightChild(), rightX, childY, spacing/2);
        }
    }
    
    /**
     * 绘制地址标尺
     */
    private void drawAddressRuler(Graphics2D g2d, int x, int y, int width) {
        g2d.setColor(Color.GRAY);
        g2d.drawLine(x, y, x + width, y);
        
        // 绘制刻度
        int totalSize = allocator.getTotalSize();
        int step = Math.max(1, totalSize / 10);
        
        for (int addr = 0; addr <= totalSize; addr += step) {
            int tickX = x + (int) ((double) addr / totalSize * width);
            g2d.drawLine(tickX, y, tickX, y + 5);
            
            g2d.setFont(new Font("Arial", Font.PLAIN, 8));
            String label = String.valueOf(addr);
            FontMetrics fm = g2d.getFontMetrics();
            g2d.drawString(label, tickX - fm.stringWidth(label)/2, y + 15);
        }
    }
    
    /**
     * 绘制图例
     */
    private void drawLegend(Graphics2D g2d) {
        int x = getWidth() - 150;
        int y = 20;
        int size = 15;
        
        g2d.setFont(new Font("Arial", Font.PLAIN, 12));
        
        // 空闲块
        g2d.setColor(FREE_COLOR);
        g2d.fillRect(x, y, size, size);
        g2d.setColor(BORDER_COLOR);
        g2d.drawRect(x, y, size, size);
        g2d.drawString("Free", x + size + 5, y + size - 2);
        
        y += 20;
        
        // 已分配块
        g2d.setColor(ALLOCATED_COLOR);
        g2d.fillRect(x, y, size, size);
        g2d.setColor(BORDER_COLOR);
        g2d.drawRect(x, y, size, size);
        g2d.drawString("Allocated", x + size + 5, y + size - 2);
        
        y += 20;
        
        // 选中块
        g2d.setColor(SELECTED_COLOR);
        g2d.fillRect(x, y, size, size);
        g2d.setColor(BORDER_COLOR);
        g2d.drawRect(x, y, size, size);
        g2d.drawString("Selected", x + size + 5, y + size - 2);
    }
    
    /**
     * 绘制统计信息
     */
    private void drawStats(Graphics2D g2d) {
        BuddySystemAllocator.MemoryStats stats = allocator.getMemoryStats();
        
        int x = 20;
        int y = getHeight() - 80;
        
        g2d.setColor(TEXT_COLOR);
        g2d.setFont(new Font("Arial", Font.PLAIN, 12));
        
        g2d.drawString(String.format("Total: %d bytes", stats.getTotalSize()), x, y);
        g2d.drawString(String.format("Allocated: %d bytes (%.1f%%)", 
                stats.getAllocatedSize(), stats.getUtilizationRatio() * 100), x, y + 15);
        g2d.drawString(String.format("Free: %d bytes", stats.getFreeSize()), x, y + 30);
        g2d.drawString(String.format("Fragments: %d", stats.getFragmentationCount()), x, y + 45);
    }
    
    /**
     * 处理鼠标点击
     */
    private void handleMouseClick(int mouseX, int mouseY) {
        if (showTree) {
            // 树形视图的点击处理
            selectedBlock = findClickedTreeNode(allocator.getRootBlock(), mouseX, mouseY, 
                    getWidth() / 2, 50, getWidth() / 4);
        } else {
            // 线性视图的点击处理
            selectedBlock = findClickedLinearBlock(mouseX, mouseY);
        }
        repaint();
    }
    
    /**
     * 在线性视图中查找被点击的块
     */
    private MemoryBlock findClickedLinearBlock(int mouseX, int mouseY) {
        List<MemoryBlock> leafBlocks = getLeafBlocks();
        leafBlocks.sort((a, b) -> Integer.compare(a.getStartAddress(), b.getStartAddress()));
        
        int totalWidth = getWidth() - 40;
        int startX = 20;
        int startY = 50;
        
        if (mouseY < startY || mouseY > startY + blockHeight) {
            return null;
        }
        
        for (MemoryBlock block : leafBlocks) {
            int blockWidth = (int) ((double) block.getSize() / allocator.getTotalSize() * totalWidth);
            
            if (mouseX >= startX && mouseX <= startX + blockWidth) {
                return block;
            }
            
            startX += blockWidth + blockSpacing;
        }
        
        return null;
    }
    
    /**
     * 在树形视图中查找被点击的节点
     */
    private MemoryBlock findClickedTreeNode(MemoryBlock block, int mouseX, int mouseY, 
                                          int x, int y, int spacing) {
        if (block == null) return null;
        
        int nodeWidth = Math.max(80, block.getSize() / 4);
        int nodeHeight = 25;
        
        // 检查是否点击了当前节点
        if (mouseX >= x - nodeWidth/2 && mouseX <= x + nodeWidth/2 &&
            mouseY >= y && mouseY <= y + nodeHeight) {
            return block;
        }
        
        // 检查子节点
        if (block.getLeftChild() != null) {
            int childY = y + levelSpacing;
            int leftX = x - spacing/2;
            int rightX = x + spacing/2;
            
            MemoryBlock leftResult = findClickedTreeNode(block.getLeftChild(), mouseX, mouseY, 
                    leftX, childY, spacing/2);
            if (leftResult != null) return leftResult;
            
            MemoryBlock rightResult = findClickedTreeNode(block.getRightChild(), mouseX, mouseY, 
                    rightX, childY, spacing/2);
            if (rightResult != null) return rightResult;
        }
        
        return null;
    }
    
    /**
     * 获取所有叶子节点
     */
    private List<MemoryBlock> getLeafBlocks() {
        return allocator.getAllBlocks().stream()
                .filter(MemoryBlock::isLeaf)
                .collect(java.util.stream.Collectors.toList());
    }
    
    // 设置方法
    public void setShowAddresses(boolean showAddresses) {
        this.showAddresses = showAddresses;
        repaint();
    }
    
    public void setShowTree(boolean showTree) {
        this.showTree = showTree;
        repaint();
    }
    
    public MemoryBlock getSelectedBlock() {
        return selectedBlock;
    }
    
    public void setSelectedBlock(MemoryBlock block) {
        this.selectedBlock = block;
        repaint();
    }
}

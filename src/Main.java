/**
 * 伙伴系统内存分配器主程序入口
 *
 * 这是一个完整的伙伴系统内存分配器实现，包含：
 * 1. 核心算法：分裂、合并、分配、回收
 * 2. 图形用户界面：直观的可视化展示
 * 3. 演示功能：自动演示分配过程
 * 4. 统计信息：内存使用率、碎片统计等
 *
 * 使用说明：
 * - 运行程序会先显示控制台演示，然后启动图形界面
 * - 在图形界面中可以手动分配/释放内存，观察伙伴系统工作过程
 * - 支持线性视图和树形视图两种显示模式
 * - 点击内存块可查看详细信息
 */
public class Main {
    public static void main(String[] args) {
        System.out.println("欢迎使用伙伴系统内存分配器演示程序！");
        System.out.println("========================================");
        System.out.println();
        System.out.println("程序功能：");
        System.out.println("1. 实现完整的伙伴系统内存分配算法");
        System.out.println("2. 提供直观的图形化界面");
        System.out.println("3. 支持内存分配、释放、可视化展示");
        System.out.println("4. 包含详细的操作历史和统计信息");
        System.out.println();
        System.out.println("即将启动演示程序...");
        System.out.println();

        // 启动演示程序
        AllocationDemo.main(args);
    }
}
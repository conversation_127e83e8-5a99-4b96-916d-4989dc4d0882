import java.util.*;

/**
 * 伙伴系统内存分配器
 * 实现伙伴系统的核心算法：分裂、合并、分配、回收
 */
public class BuddySystemAllocator {
    private MemoryBlock rootBlock;           // 根内存块
    private int totalSize;                   // 总内存大小
    private List<String> operationHistory;   // 操作历史
    private Map<String, MemoryBlock> allocatedBlocks; // 已分配块的映射
    private List<BuddySystemListener> listeners; // 监听器列表
    
    /**
     * 伙伴系统事件监听器接口
     */
    public interface BuddySystemListener {
        void onBlockAllocated(MemoryBlock block, String requestId);
        void onBlockDeallocated(MemoryBlock block, String requestId);
        void onBlockSplit(MemoryBlock parent, MemoryBlock left, MemoryBlock right);
        void onBlockMerged(MemoryBlock merged, MemoryBlock buddy);
    }
    
    /**
     * 构造函数
     * @param totalSize 总内存大小（必须是2的幂）
     */
    public BuddySystemAllocator(int totalSize) {
        if (!isPowerOfTwo(totalSize)) {
            throw new IllegalArgumentException("Total size must be a power of 2");
        }
        
        this.totalSize = totalSize;
        this.rootBlock = new MemoryBlock(0, totalSize, 0);
        this.operationHistory = new ArrayList<>();
        this.allocatedBlocks = new HashMap<>();
        this.listeners = new ArrayList<>();
        
        addOperation("Initialized buddy system with size " + totalSize);
    }
    
    /**
     * 添加事件监听器
     */
    public void addListener(BuddySystemListener listener) {
        listeners.add(listener);
    }
    
    /**
     * 移除事件监听器
     */
    public void removeListener(BuddySystemListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * 分配内存
     * @param requestSize 请求的内存大小
     * @param requestId 请求标识符
     * @return 分配的内存块，如果分配失败返回null
     */
    public MemoryBlock allocate(int requestSize, String requestId) {
        if (requestSize <= 0) {
            addOperation("Failed to allocate: invalid size " + requestSize);
            return null;
        }
        
        // 找到最小的满足要求的2的幂大小
        int actualSize = nextPowerOfTwo(requestSize);
        
        // 查找合适的块
        MemoryBlock block = findSuitableBlock(rootBlock, actualSize);
        
        if (block == null) {
            addOperation("Failed to allocate " + requestSize + " bytes: no suitable block found");
            return null;
        }
        
        // 分裂块直到达到所需大小
        while (block.getSize() > actualSize) {
            splitBlock(block);
            block = block.getLeftChild(); // 使用左子块
        }
        
        // 分配块
        block.allocate(requestId);
        allocatedBlocks.put(requestId, block);
        
        addOperation("Allocated " + actualSize + " bytes to " + requestId + 
                    " at address " + block.getStartAddress());
        
        // 通知监听器
        for (BuddySystemListener listener : listeners) {
            listener.onBlockAllocated(block, requestId);
        }
        
        return block;
    }
    
    /**
     * 释放内存
     * @param requestId 请求标识符
     * @return 是否成功释放
     */
    public boolean deallocate(String requestId) {
        MemoryBlock block = allocatedBlocks.get(requestId);
        
        if (block == null) {
            addOperation("Failed to deallocate: " + requestId + " not found");
            return false;
        }
        
        // 释放块
        block.deallocate();
        allocatedBlocks.remove(requestId);
        
        addOperation("Deallocated " + block.getSize() + " bytes from " + requestId);
        
        // 通知监听器
        for (BuddySystemListener listener : listeners) {
            listener.onBlockDeallocated(block, requestId);
        }
        
        // 尝试合并
        mergeBlock(block);
        
        return true;
    }
    
    /**
     * 查找合适的空闲块
     */
    private MemoryBlock findSuitableBlock(MemoryBlock block, int size) {
        if (block == null) {
            return null;
        }
        
        // 如果当前块已分配，返回null
        if (block.isAllocated()) {
            return null;
        }
        
        // 如果是叶子节点且大小合适
        if (block.isLeaf() && block.getSize() >= size) {
            return block;
        }
        
        // 如果有子节点，递归查找
        if (!block.isLeaf()) {
            MemoryBlock leftResult = findSuitableBlock(block.getLeftChild(), size);
            if (leftResult != null) {
                return leftResult;
            }
            return findSuitableBlock(block.getRightChild(), size);
        }
        
        return null;
    }
    
    /**
     * 分裂内存块
     */
    private void splitBlock(MemoryBlock block) {
        if (!block.canSplit()) {
            return;
        }
        
        block.split();
        
        addOperation("Split block at " + block.getStartAddress() + 
                    " (size " + block.getSize() + ") into two blocks of size " + 
                    block.getLeftChild().getSize());
        
        // 通知监听器
        for (BuddySystemListener listener : listeners) {
            listener.onBlockSplit(block, block.getLeftChild(), block.getRightChild());
        }
    }
    
    /**
     * 合并内存块
     */
    private void mergeBlock(MemoryBlock block) {
        if (block.getParent() == null) {
            return; // 根节点无法合并
        }
        
        MemoryBlock parent = block.getParent();
        MemoryBlock buddy = block.getBuddy();
        
        // 检查是否可以合并
        if (buddy != null && !buddy.isAllocated() && buddy.isLeaf() && block.isLeaf()) {
            // 合并
            parent.merge();
            
            addOperation("Merged blocks at " + Math.min(block.getStartAddress(), buddy.getStartAddress()) + 
                        " (size " + block.getSize() + " each) into block of size " + parent.getSize());
            
            // 通知监听器
            for (BuddySystemListener listener : listeners) {
                listener.onBlockMerged(parent, buddy);
            }
            
            // 递归尝试向上合并
            mergeBlock(parent);
        }
    }
    
    /**
     * 检查是否是2的幂
     */
    private boolean isPowerOfTwo(int n) {
        return n > 0 && (n & (n - 1)) == 0;
    }
    
    /**
     * 找到大于等于n的最小2的幂
     */
    private int nextPowerOfTwo(int n) {
        if (n <= 1) return 1;
        return Integer.highestOneBit(n - 1) << 1;
    }
    
    /**
     * 添加操作记录
     */
    private void addOperation(String operation) {
        operationHistory.add(new Date() + ": " + operation);
    }
    
    /**
     * 获取内存使用统计
     */
    public MemoryStats getMemoryStats() {
        return new MemoryStats(rootBlock, totalSize);
    }
    
    /**
     * 获取所有内存块（用于可视化）
     */
    public List<MemoryBlock> getAllBlocks() {
        List<MemoryBlock> blocks = new ArrayList<>();
        collectBlocks(rootBlock, blocks);
        return blocks;
    }
    
    /**
     * 递归收集所有块
     */
    private void collectBlocks(MemoryBlock block, List<MemoryBlock> blocks) {
        if (block == null) return;
        
        blocks.add(block);
        collectBlocks(block.getLeftChild(), blocks);
        collectBlocks(block.getRightChild(), blocks);
    }
    
    // Getters
    public MemoryBlock getRootBlock() { return rootBlock; }
    public int getTotalSize() { return totalSize; }
    public List<String> getOperationHistory() { return new ArrayList<>(operationHistory); }
    public Map<String, MemoryBlock> getAllocatedBlocks() { return new HashMap<>(allocatedBlocks); }
    
    /**
     * 内存统计信息类
     */
    public static class MemoryStats {
        private int totalSize;
        private int allocatedSize;
        private int freeSize;
        private int fragmentationCount;
        private double fragmentationRatio;
        
        public MemoryStats(MemoryBlock root, int totalSize) {
            this.totalSize = totalSize;
            calculateStats(root);
            this.freeSize = totalSize - allocatedSize;
            this.fragmentationRatio = fragmentationCount > 0 ? 
                (double) fragmentationCount / totalSize : 0.0;
        }
        
        private void calculateStats(MemoryBlock block) {
            if (block == null) return;
            
            if (block.isLeaf()) {
                if (block.isAllocated()) {
                    allocatedSize += block.getSize();
                } else {
                    fragmentationCount++;
                }
            }
            
            calculateStats(block.getLeftChild());
            calculateStats(block.getRightChild());
        }
        
        // Getters
        public int getTotalSize() { return totalSize; }
        public int getAllocatedSize() { return allocatedSize; }
        public int getFreeSize() { return freeSize; }
        public int getFragmentationCount() { return fragmentationCount; }
        public double getFragmentationRatio() { return fragmentationRatio; }
        public double getUtilizationRatio() { return (double) allocatedSize / totalSize; }
    }
}

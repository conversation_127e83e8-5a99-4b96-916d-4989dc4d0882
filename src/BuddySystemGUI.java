import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.Map;

/**
 * 伙伴系统内存分配器图形用户界面
 * 提供直观的操作界面和可视化展示
 */
public class BuddySystemGUI extends JFrame implements BuddySystemAllocator.BuddySystemListener {
    private BuddySystemAllocator allocator;
    private MemoryVisualizer visualizer;
    
    // 控制面板组件
    private JTextField sizeField;
    private JTextField requestIdField;
    private JComboBox<String> allocatedBlocksCombo;
    private JButton allocateButton;
    private JButton deallocateButton;
    private JButton resetButton;
    
    // 显示选项
    private JCheckBox showAddressesCheck;
    private JCheckBox showTreeCheck;
    
    // 信息显示区域
    private JTextArea historyArea;
    private JTextArea blockInfoArea;
    private JLabel statsLabel;
    
    // 演示控制
    private JButton demoButton;
    private JSlider memorySizeSlider;
    private boolean isDemoRunning = false;
    
    public BuddySystemGUI() {
        initializeAllocator();
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        updateDisplay();
        
        setTitle("伙伴系统内存分配器演示");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);
    }
    
    /**
     * 初始化内存分配器
     */
    private void initializeAllocator() {
        allocator = new BuddySystemAllocator(1024); // 默认1KB
        allocator.addListener(this);
    }
    
    /**
     * 初始化界面组件
     */
    private void initializeComponents() {
        // 创建可视化组件
        visualizer = new MemoryVisualizer(allocator);
        
        // 控制面板组件
        sizeField = new JTextField("64", 8);
        requestIdField = new JTextField("Request1", 10);
        allocatedBlocksCombo = new JComboBox<>();
        allocateButton = new JButton("分配内存");
        deallocateButton = new JButton("释放内存");
        resetButton = new JButton("重置系统");
        
        // 显示选项
        showAddressesCheck = new JCheckBox("显示地址", true);
        showTreeCheck = new JCheckBox("树形视图", false);
        
        // 信息显示区域
        historyArea = new JTextArea(8, 30);
        historyArea.setEditable(false);
        historyArea.setFont(new Font("Monospaced", Font.PLAIN, 11));
        
        blockInfoArea = new JTextArea(6, 30);
        blockInfoArea.setEditable(false);
        blockInfoArea.setFont(new Font("Monospaced", Font.PLAIN, 11));
        
        statsLabel = new JLabel();
        statsLabel.setFont(new Font("Arial", Font.PLAIN, 12));
        
        // 演示控制
        demoButton = new JButton("运行演示");
        memorySizeSlider = new JSlider(6, 12, 10); // 2^6 到 2^12 (64 到 4096)
        memorySizeSlider.setMajorTickSpacing(1);
        memorySizeSlider.setPaintTicks(true);
        memorySizeSlider.setPaintLabels(true);
        
        // 设置滑块标签
        java.util.Hashtable<Integer, JLabel> labelTable = new java.util.Hashtable<>();
        for (int i = 6; i <= 12; i++) {
            labelTable.put(i, new JLabel(String.valueOf(1 << i)));
        }
        memorySizeSlider.setLabelTable(labelTable);
    }
    
    /**
     * 设置布局
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // 主面板
        JPanel mainPanel = new JPanel(new BorderLayout());
        
        // 可视化面板
        JPanel visualPanel = new JPanel(new BorderLayout());
        visualPanel.setBorder(new TitledBorder("内存布局可视化"));
        visualPanel.add(visualizer, BorderLayout.CENTER);
        
        // 显示选项面板
        JPanel optionsPanel = new JPanel(new FlowLayout());
        optionsPanel.add(showAddressesCheck);
        optionsPanel.add(showTreeCheck);
        visualPanel.add(optionsPanel, BorderLayout.SOUTH);
        
        mainPanel.add(visualPanel, BorderLayout.CENTER);
        
        // 右侧控制面板
        JPanel rightPanel = new JPanel(new BorderLayout());
        rightPanel.setPreferredSize(new Dimension(350, 0));
        
        // 控制面板
        JPanel controlPanel = createControlPanel();
        rightPanel.add(controlPanel, BorderLayout.NORTH);
        
        // 信息面板
        JPanel infoPanel = createInfoPanel();
        rightPanel.add(infoPanel, BorderLayout.CENTER);
        
        mainPanel.add(rightPanel, BorderLayout.EAST);
        
        // 底部状态面板
        JPanel statusPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        statusPanel.add(statsLabel);
        mainPanel.add(statusPanel, BorderLayout.SOUTH);
        
        add(mainPanel, BorderLayout.CENTER);
        
        // 顶部工具栏
        JPanel toolbarPanel = createToolbarPanel();
        add(toolbarPanel, BorderLayout.NORTH);
    }
    
    /**
     * 创建控制面板
     */
    private JPanel createControlPanel() {
        JPanel panel = new JPanel();
        panel.setBorder(new TitledBorder("内存操作"));
        panel.setLayout(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        // 分配内存
        gbc.gridx = 0; gbc.gridy = 0;
        panel.add(new JLabel("请求大小:"), gbc);
        gbc.gridx = 1;
        panel.add(sizeField, gbc);
        
        gbc.gridx = 0; gbc.gridy = 1;
        panel.add(new JLabel("请求ID:"), gbc);
        gbc.gridx = 1;
        panel.add(requestIdField, gbc);
        
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 2;
        panel.add(allocateButton, gbc);
        
        // 分隔线
        gbc.gridy = 3; gbc.fill = GridBagConstraints.HORIZONTAL;
        panel.add(new JSeparator(), gbc);
        
        // 释放内存
        gbc.gridy = 4; gbc.gridwidth = 1; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("已分配块:"), gbc);
        gbc.gridx = 1;
        panel.add(allocatedBlocksCombo, gbc);
        
        gbc.gridx = 0; gbc.gridy = 5; gbc.gridwidth = 2;
        panel.add(deallocateButton, gbc);
        
        // 重置按钮
        gbc.gridy = 6;
        panel.add(resetButton, gbc);
        
        return panel;
    }
    
    /**
     * 创建信息面板
     */
    private JPanel createInfoPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        // 创建选项卡面板
        JTabbedPane tabbedPane = new JTabbedPane();
        
        // 操作历史选项卡
        JScrollPane historyScroll = new JScrollPane(historyArea);
        historyScroll.setBorder(new TitledBorder("操作历史"));
        tabbedPane.addTab("历史", historyScroll);
        
        // 块信息选项卡
        JScrollPane blockInfoScroll = new JScrollPane(blockInfoArea);
        blockInfoScroll.setBorder(new TitledBorder("选中块信息"));
        tabbedPane.addTab("块信息", blockInfoScroll);
        
        panel.add(tabbedPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 创建工具栏面板
     */
    private JPanel createToolbarPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        panel.setBorder(new TitledBorder("系统配置"));
        
        panel.add(new JLabel("内存大小:"));
        panel.add(memorySizeSlider);
        panel.add(demoButton);
        
        return panel;
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        // 分配按钮
        allocateButton.addActionListener(e -> allocateMemory());
        
        // 释放按钮
        deallocateButton.addActionListener(e -> deallocateMemory());
        
        // 重置按钮
        resetButton.addActionListener(e -> resetSystem());
        
        // 显示选项
        showAddressesCheck.addActionListener(e -> {
            visualizer.setShowAddresses(showAddressesCheck.isSelected());
        });
        
        showTreeCheck.addActionListener(e -> {
            visualizer.setShowTree(showTreeCheck.isSelected());
        });
        
        // 内存大小滑块
        memorySizeSlider.addChangeListener(e -> {
            if (!memorySizeSlider.getValueIsAdjusting()) {
                int newSize = 1 << memorySizeSlider.getValue();
                resetSystem(newSize);
            }
        });
        
        // 演示按钮
        demoButton.addActionListener(e -> runDemo());
        
        // 可视化器选择事件
        visualizer.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent e) {
                updateBlockInfo();
            }
        });
    }
    
    /**
     * 分配内存
     */
    private void allocateMemory() {
        try {
            int size = Integer.parseInt(sizeField.getText().trim());
            String requestId = requestIdField.getText().trim();
            
            if (requestId.isEmpty()) {
                JOptionPane.showMessageDialog(this, "请输入请求ID", "错误", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            MemoryBlock block = allocator.allocate(size, requestId);
            
            if (block == null) {
                JOptionPane.showMessageDialog(this, "内存分配失败：没有足够的连续空间", 
                        "分配失败", JOptionPane.WARNING_MESSAGE);
            } else {
                // 生成下一个请求ID
                generateNextRequestId();
            }
            
            updateDisplay();
            
        } catch (NumberFormatException ex) {
            JOptionPane.showMessageDialog(this, "请输入有效的数字", "错误", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * 释放内存
     */
    private void deallocateMemory() {
        String selectedItem = (String) allocatedBlocksCombo.getSelectedItem();
        if (selectedItem == null) {
            JOptionPane.showMessageDialog(this, "请选择要释放的内存块", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        String requestId = selectedItem.split(" ")[0]; // 提取请求ID
        boolean success = allocator.deallocate(requestId);
        
        if (!success) {
            JOptionPane.showMessageDialog(this, "内存释放失败", "错误", JOptionPane.ERROR_MESSAGE);
        }
        
        updateDisplay();
    }
    
    /**
     * 重置系统
     */
    private void resetSystem() {
        resetSystem(1 << memorySizeSlider.getValue());
    }
    
    private void resetSystem(int newSize) {
        allocator = new BuddySystemAllocator(newSize);
        allocator.addListener(this);
        visualizer = new MemoryVisualizer(allocator);
        
        // 重新设置可视化面板
        Container parent = getContentPane();
        Component[] components = parent.getComponents();
        for (Component comp : components) {
            if (comp instanceof JPanel) {
                updateVisualizerInPanel((JPanel) comp);
            }
        }
        
        updateDisplay();
        repaint();
    }
    
    /**
     * 递归更新可视化组件
     */
    private void updateVisualizerInPanel(JPanel panel) {
        Component[] components = panel.getComponents();
        for (int i = 0; i < components.length; i++) {
            if (components[i] instanceof MemoryVisualizer) {
                panel.remove(i);
                panel.add(visualizer, i);
                visualizer.setShowAddresses(showAddressesCheck.isSelected());
                visualizer.setShowTree(showTreeCheck.isSelected());
                return;
            } else if (components[i] instanceof JPanel) {
                updateVisualizerInPanel((JPanel) components[i]);
            }
        }
    }
    
    /**
     * 运行演示
     */
    private void runDemo() {
        if (isDemoRunning) return;
        
        isDemoRunning = true;
        demoButton.setEnabled(false);
        
        // 在后台线程运行演示
        SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() throws Exception {
                // 演示序列
                String[] demoRequests = {"Demo1", "Demo2", "Demo3", "Demo4"};
                int[] demoSizes = {64, 128, 32, 256};
                
                for (int i = 0; i < demoRequests.length; i++) {
                    Thread.sleep(1000);
                    final int index = i;
                    SwingUtilities.invokeLater(() -> {
                        allocator.allocate(demoSizes[index % demoSizes.length],
                                demoRequests[index % demoRequests.length]);
                        updateDisplay();
                    });
                }
                
                Thread.sleep(2000);
                
                // 释放一些块
                for (int i = 0; i < 2; i++) {
                    Thread.sleep(1000);
                    SwingUtilities.invokeLater(() -> {
                        if (!allocator.getAllocatedBlocks().isEmpty()) {
                            String firstKey = allocator.getAllocatedBlocks().keySet().iterator().next();
                            allocator.deallocate(firstKey);
                            updateDisplay();
                        }
                    });
                }
                
                return null;
            }
            
            @Override
            protected void done() {
                isDemoRunning = false;
                demoButton.setEnabled(true);
            }
        };
        
        worker.execute();
    }
    
    /**
     * 生成下一个请求ID
     */
    private void generateNextRequestId() {
        String current = requestIdField.getText();
        if (current.matches(".*\\d+$")) {
            String prefix = current.replaceAll("\\d+$", "");
            String numberStr = current.substring(prefix.length());
            int number = Integer.parseInt(numberStr) + 1;
            requestIdField.setText(prefix + number);
        }
    }
    
    /**
     * 更新显示
     */
    private void updateDisplay() {
        updateAllocatedBlocksCombo();
        updateHistory();
        updateStats();
        updateBlockInfo();
        visualizer.repaint();
    }
    
    /**
     * 更新已分配块下拉框
     */
    private void updateAllocatedBlocksCombo() {
        allocatedBlocksCombo.removeAllItems();
        Map<String, MemoryBlock> allocated = allocator.getAllocatedBlocks();
        
        for (Map.Entry<String, MemoryBlock> entry : allocated.entrySet()) {
            MemoryBlock block = entry.getValue();
            String item = String.format("%s (Size: %d, Addr: %d-%d)", 
                    entry.getKey(), block.getSize(), 
                    block.getStartAddress(), block.getEndAddress());
            allocatedBlocksCombo.addItem(item);
        }
        
        deallocateButton.setEnabled(allocatedBlocksCombo.getItemCount() > 0);
    }
    
    /**
     * 更新操作历史
     */
    private void updateHistory() {
        StringBuilder sb = new StringBuilder();
        java.util.List<String> history = allocator.getOperationHistory();
        
        // 只显示最近的20条记录
        int start = Math.max(0, history.size() - 20);
        for (int i = start; i < history.size(); i++) {
            sb.append(history.get(i)).append("\n");
        }
        
        historyArea.setText(sb.toString());
        historyArea.setCaretPosition(historyArea.getDocument().getLength());
    }
    
    /**
     * 更新统计信息
     */
    private void updateStats() {
        BuddySystemAllocator.MemoryStats stats = allocator.getMemoryStats();
        String statsText = String.format(
                "总内存: %d | 已用: %d (%.1f%%) | 空闲: %d | 碎片: %d",
                stats.getTotalSize(),
                stats.getAllocatedSize(),
                stats.getUtilizationRatio() * 100,
                stats.getFreeSize(),
                stats.getFragmentationCount()
        );
        statsLabel.setText(statsText);
    }
    
    /**
     * 更新块信息
     */
    private void updateBlockInfo() {
        MemoryBlock selected = visualizer.getSelectedBlock();
        if (selected == null) {
            blockInfoArea.setText("点击内存块查看详细信息");
            return;
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("=== 内存块详细信息 ===\n");
        sb.append(String.format("起始地址: %d\n", selected.getStartAddress()));
        sb.append(String.format("结束地址: %d\n", selected.getEndAddress()));
        sb.append(String.format("大小: %d 字节\n", selected.getSize()));
        sb.append(String.format("层级: %d\n", selected.getLevel()));
        sb.append(String.format("状态: %s\n", selected.isAllocated() ? "已分配" : "空闲"));
        
        if (selected.isAllocated() && selected.getAllocatedTo() != null) {
            sb.append(String.format("分配给: %s\n", selected.getAllocatedTo()));
        }
        
        sb.append(String.format("是否叶子节点: %s\n", selected.isLeaf() ? "是" : "否"));
        
        if (selected.getBuddy() != null) {
            MemoryBlock buddy = selected.getBuddy();
            sb.append(String.format("伙伴块: %d-%d (大小: %d)\n", 
                    buddy.getStartAddress(), buddy.getEndAddress(), buddy.getSize()));
        }
        
        blockInfoArea.setText(sb.toString());
    }
    
    // 实现BuddySystemListener接口
    @Override
    public void onBlockAllocated(MemoryBlock block, String requestId) {
        SwingUtilities.invokeLater(this::updateDisplay);
    }
    
    @Override
    public void onBlockDeallocated(MemoryBlock block, String requestId) {
        SwingUtilities.invokeLater(this::updateDisplay);
    }
    
    @Override
    public void onBlockSplit(MemoryBlock parent, MemoryBlock left, MemoryBlock right) {
        SwingUtilities.invokeLater(this::updateDisplay);
    }
    
    @Override
    public void onBlockMerged(MemoryBlock merged, MemoryBlock buddy) {
        SwingUtilities.invokeLater(this::updateDisplay);
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new BuddySystemGUI().setVisible(true);
        });
    }
}

/**
 * 伙伴系统内存分配器演示类
 * 展示各种分配和回收场景，验证算法正确性
 */
public class AllocationDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 伙伴系统内存分配器演示 ===\n");
        
        // 运行各种测试场景
        testBasicAllocation();
        testFragmentation();
        testMerging();
        testEdgeCases();
        testPerformance();
        
        System.out.println("\n=== 启动图形界面 ===");
        BuddySystemGUI.main(args);
    }
    
    /**
     * 测试基本分配功能
     */
    private static void testBasicAllocation() {
        System.out.println("1. 基本分配测试");
        System.out.println("================");
        
        BuddySystemAllocator allocator = new BuddySystemAllocator(1024);
        
        // 分配不同大小的内存块
        System.out.println("初始状态:");
        printMemoryState(allocator);
        
        System.out.println("\n分配 100 字节给 Request1:");
        MemoryBlock block1 = allocator.allocate(100, "Request1");
        printAllocationResult(block1);
        printMemoryState(allocator);
        
        System.out.println("\n分配 200 字节给 Request2:");
        MemoryBlock block2 = allocator.allocate(200, "Request2");
        printAllocationResult(block2);
        printMemoryState(allocator);
        
        System.out.println("\n分配 50 字节给 Request3:");
        MemoryBlock block3 = allocator.allocate(50, "Request3");
        printAllocationResult(block3);
        printMemoryState(allocator);
        
        System.out.println("\n释放 Request2:");
        boolean success = allocator.deallocate("Request2");
        System.out.println("释放结果: " + (success ? "成功" : "失败"));
        printMemoryState(allocator);
        
        System.out.println();
    }
    
    /**
     * 测试内存碎片情况
     */
    private static void testFragmentation() {
        System.out.println("2. 内存碎片测试");
        System.out.println("================");
        
        BuddySystemAllocator allocator = new BuddySystemAllocator(512);
        
        // 分配多个小块造成碎片
        System.out.println("分配多个小块:");
        for (int i = 1; i <= 6; i++) {
            MemoryBlock block = allocator.allocate(32, "Small" + i);
            System.out.printf("Small%d: %s\n", i, 
                    block != null ? "成功 [" + block.getStartAddress() + "-" + block.getEndAddress() + "]" : "失败");
        }
        
        printMemoryState(allocator);
        
        // 释放部分块
        System.out.println("\n释放 Small2, Small4, Small6:");
        allocator.deallocate("Small2");
        allocator.deallocate("Small4");
        allocator.deallocate("Small6");
        
        printMemoryState(allocator);
        
        // 尝试分配大块
        System.out.println("\n尝试分配 128 字节:");
        MemoryBlock bigBlock = allocator.allocate(128, "BigBlock");
        printAllocationResult(bigBlock);
        printMemoryState(allocator);
        
        System.out.println();
    }
    
    /**
     * 测试块合并功能
     */
    private static void testMerging() {
        System.out.println("3. 块合并测试");
        System.out.println("==============");
        
        BuddySystemAllocator allocator = new BuddySystemAllocator(256);
        
        // 分配相邻的块
        System.out.println("分配四个 32 字节的块:");
        MemoryBlock[] blocks = new MemoryBlock[4];
        for (int i = 0; i < 4; i++) {
            blocks[i] = allocator.allocate(32, "Block" + (i + 1));
            System.out.printf("Block%d: [%d-%d]\n", i + 1, 
                    blocks[i].getStartAddress(), blocks[i].getEndAddress());
        }
        
        printMemoryState(allocator);
        
        // 按顺序释放，观察合并过程
        System.out.println("\n按顺序释放块，观察合并:");
        for (int i = 0; i < 4; i++) {
            System.out.printf("释放 Block%d:\n", i + 1);
            allocator.deallocate("Block" + (i + 1));
            printMemoryState(allocator);
        }
        
        System.out.println();
    }
    
    /**
     * 测试边界情况
     */
    private static void testEdgeCases() {
        System.out.println("4. 边界情况测试");
        System.out.println("================");
        
        BuddySystemAllocator allocator = new BuddySystemAllocator(128);
        
        // 测试分配整个内存
        System.out.println("分配整个内存 (128 字节):");
        MemoryBlock fullBlock = allocator.allocate(128, "FullBlock");
        printAllocationResult(fullBlock);
        
        // 尝试再分配
        System.out.println("\n尝试再分配 32 字节:");
        MemoryBlock failBlock = allocator.allocate(32, "FailBlock");
        printAllocationResult(failBlock);
        
        // 释放后再分配
        System.out.println("\n释放整个内存后再分配 64 字节:");
        allocator.deallocate("FullBlock");
        MemoryBlock halfBlock = allocator.allocate(64, "HalfBlock");
        printAllocationResult(halfBlock);
        
        printMemoryState(allocator);
        
        // 测试无效操作
        System.out.println("\n测试无效操作:");
        System.out.println("尝试释放不存在的块:");
        boolean invalidFree = allocator.deallocate("NonExistent");
        System.out.println("结果: " + (invalidFree ? "成功" : "失败"));
        
        System.out.println("尝试分配 0 字节:");
        MemoryBlock zeroBlock = allocator.allocate(0, "ZeroBlock");
        printAllocationResult(zeroBlock);
        
        System.out.println();
    }
    
    /**
     * 性能测试
     */
    private static void testPerformance() {
        System.out.println("5. 性能测试");
        System.out.println("============");
        
        BuddySystemAllocator allocator = new BuddySystemAllocator(4096);
        
        // 大量分配测试
        long startTime = System.nanoTime();
        
        System.out.println("执行 1000 次分配操作...");
        int successCount = 0;
        for (int i = 0; i < 1000; i++) {
            int size = (int) (Math.random() * 100) + 1; // 1-100 字节
            MemoryBlock block = allocator.allocate(size, "Perf" + i);
            if (block != null) {
                successCount++;
            }
        }
        
        long allocTime = System.nanoTime() - startTime;
        
        System.out.printf("分配完成: %d/1000 成功\n", successCount);
        System.out.printf("分配耗时: %.2f ms\n", allocTime / 1_000_000.0);
        
        // 释放测试
        startTime = System.nanoTime();
        
        System.out.println("释放所有已分配的块...");
        int deallocCount = 0;
        for (int i = 0; i < 1000; i++) {
            if (allocator.deallocate("Perf" + i)) {
                deallocCount++;
            }
        }
        
        long deallocTime = System.nanoTime() - startTime;
        
        System.out.printf("释放完成: %d 个块被释放\n", deallocCount);
        System.out.printf("释放耗时: %.2f ms\n", deallocTime / 1_000_000.0);
        
        printMemoryState(allocator);
        
        System.out.println();
    }
    
    /**
     * 打印分配结果
     */
    private static void printAllocationResult(MemoryBlock block) {
        if (block != null) {
            System.out.printf("分配成功: [%d-%d], 大小: %d 字节\n", 
                    block.getStartAddress(), block.getEndAddress(), block.getSize());
        } else {
            System.out.println("分配失败: 没有足够的连续空间");
        }
    }
    
    /**
     * 打印内存状态
     */
    private static void printMemoryState(BuddySystemAllocator allocator) {
        BuddySystemAllocator.MemoryStats stats = allocator.getMemoryStats();
        
        System.out.println("内存状态:");
        System.out.printf("  总大小: %d 字节\n", stats.getTotalSize());
        System.out.printf("  已分配: %d 字节 (%.1f%%)\n", 
                stats.getAllocatedSize(), stats.getUtilizationRatio() * 100);
        System.out.printf("  空闲: %d 字节\n", stats.getFreeSize());
        System.out.printf("  碎片数: %d\n", stats.getFragmentationCount());
        
        // 显示内存布局
        System.out.print("  布局: ");
        java.util.List<MemoryBlock> leafBlocks = allocator.getAllBlocks().stream()
                .filter(MemoryBlock::isLeaf)
                .sorted((a, b) -> Integer.compare(a.getStartAddress(), b.getStartAddress()))
                .collect(java.util.stream.Collectors.toList());
        
        for (MemoryBlock block : leafBlocks) {
            if (block.isAllocated()) {
                System.out.printf("[%s:%d] ", block.getAllocatedTo(), block.getSize());
            } else {
                System.out.printf("[Free:%d] ", block.getSize());
            }
        }
        System.out.println();
    }
}

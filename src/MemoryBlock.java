/**
 * 内存块类 - 表示伙伴系统中的一个内存块
 * 包含内存块的基本信息和状态
 */
public class MemoryBlock {
    private int startAddress;    // 起始地址
    private int size;           // 块大小
    private boolean isAllocated; // 是否已分配
    private int level;          // 在伙伴系统中的层级
    private MemoryBlock buddy;   // 伙伴块引用
    private MemoryBlock parent;  // 父块引用
    private MemoryBlock leftChild;  // 左子块
    private MemoryBlock rightChild; // 右子块
    private String allocatedTo;  // 分配给谁（用于演示）
    
    /**
     * 构造函数
     * @param startAddress 起始地址
     * @param size 块大小
     * @param level 层级
     */
    public MemoryBlock(int startAddress, int size, int level) {
        this.startAddress = startAddress;
        this.size = size;
        this.level = level;
        this.isAllocated = false;
        this.buddy = null;
        this.parent = null;
        this.leftChild = null;
        this.rightChild = null;
        this.allocatedTo = null;
    }
    
    /**
     * 检查是否可以分裂
     * @return 如果可以分裂返回true
     */
    public boolean canSplit() {
        return !isAllocated && size > 1 && leftChild == null && rightChild == null;
    }
    
    /**
     * 检查是否可以合并
     * @return 如果可以合并返回true
     */
    public boolean canMerge() {
        if (buddy == null || isAllocated || buddy.isAllocated) {
            return false;
        }
        // 检查伙伴块是否也是空闲的且没有子块
        return buddy.leftChild == null && buddy.rightChild == null &&
               leftChild == null && rightChild == null;
    }
    
    /**
     * 分裂内存块为两个伙伴块
     */
    public void split() {
        if (!canSplit()) {
            throw new IllegalStateException("Cannot split this block");
        }
        
        int halfSize = size / 2;
        
        // 创建左子块
        leftChild = new MemoryBlock(startAddress, halfSize, level + 1);
        leftChild.parent = this;
        
        // 创建右子块
        rightChild = new MemoryBlock(startAddress + halfSize, halfSize, level + 1);
        rightChild.parent = this;
        
        // 设置伙伴关系
        leftChild.buddy = rightChild;
        rightChild.buddy = leftChild;
    }
    
    /**
     * 合并伙伴块
     */
    public void merge() {
        if (!canMerge()) {
            throw new IllegalStateException("Cannot merge this block");
        }
        
        // 清除子块
        leftChild = null;
        rightChild = null;
        
        // 清除伙伴块的子块
        if (buddy != null) {
            buddy.leftChild = null;
            buddy.rightChild = null;
        }
    }
    
    /**
     * 分配内存块
     * @param allocatedTo 分配给谁
     */
    public void allocate(String allocatedTo) {
        if (isAllocated) {
            throw new IllegalStateException("Block is already allocated");
        }
        this.isAllocated = true;
        this.allocatedTo = allocatedTo;
    }
    
    /**
     * 释放内存块
     */
    public void deallocate() {
        this.isAllocated = false;
        this.allocatedTo = null;
    }
    
    /**
     * 检查是否是叶子节点
     */
    public boolean isLeaf() {
        return leftChild == null && rightChild == null;
    }
    
    /**
     * 获取结束地址
     */
    public int getEndAddress() {
        return startAddress + size - 1;
    }
    
    // Getters and Setters
    public int getStartAddress() { return startAddress; }
    public void setStartAddress(int startAddress) { this.startAddress = startAddress; }
    
    public int getSize() { return size; }
    public void setSize(int size) { this.size = size; }
    
    public boolean isAllocated() { return isAllocated; }
    public void setAllocated(boolean allocated) { isAllocated = allocated; }
    
    public int getLevel() { return level; }
    public void setLevel(int level) { this.level = level; }
    
    public MemoryBlock getBuddy() { return buddy; }
    public void setBuddy(MemoryBlock buddy) { this.buddy = buddy; }
    
    public MemoryBlock getParent() { return parent; }
    public void setParent(MemoryBlock parent) { this.parent = parent; }
    
    public MemoryBlock getLeftChild() { return leftChild; }
    public void setLeftChild(MemoryBlock leftChild) { this.leftChild = leftChild; }
    
    public MemoryBlock getRightChild() { return rightChild; }
    public void setRightChild(MemoryBlock rightChild) { this.rightChild = rightChild; }
    
    public String getAllocatedTo() { return allocatedTo; }
    public void setAllocatedTo(String allocatedTo) { this.allocatedTo = allocatedTo; }
    
    @Override
    public String toString() {
        return String.format("Block[%d-%d, size=%d, level=%d, allocated=%s%s]",
                startAddress, getEndAddress(), size, level, isAllocated,
                allocatedTo != null ? " to " + allocatedTo : "");
    }
}
